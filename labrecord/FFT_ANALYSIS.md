# FFT Analysis Implementation

## Overview

This implementation adds real-time FFT (Fast Fourier Transform) analysis to the labrecord application, allowing you to see dominant frequencies in the accelerometer data from both sensors.

## Features

- **Real-time FFT Analysis**: Performs FFT on the last 800 samples (1 second of data at 800 Hz sampling rate)
- **Frequency Domain Visualization**: Shows frequency spectrum up to 200 Hz for each sensor
- **Dominant Frequency Detection**: Displays the top 5 dominant frequencies with their magnitudes
- **Windowing**: Uses Hanning window to reduce spectral leakage
- **Efficient Processing**: Only analyzes X-axis data for performance (can be extended to Y and Z axes)

## How It Works

### 1. Sample Buffer
- Maintains a rolling buffer of the last `SAMPLES_PER_SECOND` (800) samples for each axis
- Automatically removes old samples when the buffer is full
- Uses `VecDeque` for efficient insertion and removal

### 2. FFT Processing
- Applies Hanning window to reduce spectral leakage
- Performs forward FFT using the `rustfft` library
- Calculates frequency bins and magnitudes
- Identifies dominant frequencies by sorting magnitudes

### 3. GUI Integration
- **Time Domain Plots**: Shows traditional accelerometer data over time
- **Frequency Domain Plots**: Shows frequency spectrum with magnitude vs frequency
- **Dominant Frequencies List**: Displays top 5 frequencies with their magnitudes in Hz

## Usage

When you run the application:

1. **Time Domain**: Left side shows traditional time-series plots for both sensors
2. **Frequency Domain**: Right side shows frequency spectrum plots for both sensors
3. **Dominant Frequencies**: Bottom section lists the top 5 dominant frequencies for each sensor

## Technical Details

### Sampling Rate
- **Sample Rate**: 800 Hz (SAMPLES_PER_SECOND)
- **Buffer Size**: 800 samples (1 second of data)
- **Frequency Resolution**: 1 Hz (800 Hz / 800 samples)
- **Nyquist Frequency**: 400 Hz (but display limited to 200 Hz for clarity)

### FFT Parameters
- **Window Function**: Hanning window
- **FFT Size**: 800 points
- **Frequency Range**: 0-200 Hz (displayed)
- **Update Rate**: Real-time (every new sample when buffer is full)

### Performance Considerations
- Only X-axis is analyzed by default (can be extended to Y and Z)
- FFT is performed only when the buffer is full (800 samples)
- Results are cached until the next FFT computation

## Extending the Implementation

### To analyze Y and Z axes:
1. Modify the GUI to show Y and Z frequency plots
2. Update the `add_samples` function to use Y and Z FFT results
3. Add separate frequency plots for each axis

### To change frequency range:
1. Modify the frequency filter in `serial.rs` (currently 200 Hz limit)
2. Update GUI plot bounds in `gui.rs`

### To adjust FFT parameters:
1. Change buffer size in `fft_analysis.rs`
2. Modify window function in `analyze_axis` method
3. Adjust dominant frequency count (currently top 5)

## Dependencies

- `rustfft = "6.2"`: High-performance FFT library for Rust

## Testing

The implementation includes comprehensive tests:
- Buffer creation and sample addition
- FFT analysis with synthetic sine wave data
- Frequency detection accuracy verification

Run tests with: `cargo test fft_analysis`
