use crate::interface::SAMPLES_PER_SECOND;
use crate::sample::Sam<PERSON>;
use rustfft::{num_complex::Complex, FftPlanner};
use std::collections::VecDeque;

/// FFT analysis results for a single axis
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct AxisFftResult {
    pub frequencies: Vec<f64>,
    pub magnitudes: Vec<f64>,
    pub dominant_frequencies: Vec<(f64, f64)>, // (frequency, magnitude) pairs
}

/// FFT analysis results for all three axes
#[derive(Debug, Clone)]
pub struct FftResult {
    pub x: AxisFftResult,
    pub y: AxisFftResult,
    pub z: AxisFftResult,
}

/// Rolling buffer for storing samples and performing FFT analysis
#[derive(Debug, <PERSON>lone)]
pub struct SampleBuffer {
    x_samples: VecDeque<f64>,
    y_samples: VecDeque<f64>,
    z_samples: VecDeque<f64>,
    buffer_size: usize,
    sample_rate: f64,
}

impl SampleBuffer {
    pub fn new() -> Self {
        let buffer_size = 10 * SAMPLES_PER_SECOND as usize;
        Self {
            x_samples: VecDeque::with_capacity(buffer_size),
            y_samples: VecDeque::with_capacity(buffer_size),
            z_samples: VecDeque::with_capacity(buffer_size),
            buffer_size,
            sample_rate: SAMPLES_PER_SECOND as f64,
        }
    }

    /// Add a new sample to the buffer
    pub fn add_sample(&mut self, sample: &Sample) {
        // Add new samples
        self.x_samples.push_back(sample.x as f64);
        self.y_samples.push_back(sample.y as f64);
        self.z_samples.push_back(sample.z as f64);

        // Remove old samples if buffer is full
        if self.x_samples.len() > self.buffer_size {
            self.x_samples.pop_front();
            self.y_samples.pop_front();
            self.z_samples.pop_front();
        }
    }

    /// Check if buffer has enough samples for FFT analysis
    pub fn is_ready(&self) -> bool {
        self.x_samples.len() >= self.buffer_size
    }

    /// Perform FFT analysis on the current buffer
    pub fn analyze(&self) -> Option<FftResult> {
        if !self.is_ready() {
            return None;
        }

        Some(FftResult {
            x: self.analyze_axis(&self.x_samples),
            y: self.analyze_axis(&self.y_samples),
            z: self.analyze_axis(&self.z_samples),
        })
    }

    /// Perform FFT analysis on a single axis
    fn analyze_axis(&self, samples: &VecDeque<f64>) -> AxisFftResult {
        let n = samples.len();

        // Convert samples to complex numbers
        let mut buffer: Vec<Complex<f64>> = samples.iter().map(|&x| Complex::new(x, 0.0)).collect();

        // Apply Hanning window to reduce spectral leakage
        for (i, sample) in buffer.iter_mut().enumerate() {
            let window =
                0.5 * (1.0 - (2.0 * std::f64::consts::PI * i as f64 / (n - 1) as f64).cos());
            sample.re *= window;
        }

        // Perform FFT
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(n);
        fft.process(&mut buffer);

        // Calculate frequencies and magnitudes
        let mut frequencies = Vec::with_capacity(n / 2);
        let mut magnitudes = Vec::with_capacity(n / 2);

        for i in 0..n / 2 {
            let freq = i as f64 * self.sample_rate / n as f64;
            let magnitude = buffer[i].norm() / n as f64;

            frequencies.push(freq);
            magnitudes.push(magnitude);
        }

        // Find dominant frequencies (top 5)
        let mut freq_mag_pairs: Vec<(f64, f64)> = frequencies
            .iter()
            .zip(magnitudes.iter())
            .map(|(&f, &m)| (f, m))
            .collect();

        // Sort by magnitude in descending order
        freq_mag_pairs.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());

        // Take top 5 dominant frequencies, excluding DC component (0 Hz)
        let dominant_frequencies: Vec<(f64, f64)> = freq_mag_pairs
            .into_iter()
            .filter(|(freq, _)| *freq > 0.0) // Exclude DC component
            .take(5)
            .collect();

        AxisFftResult {
            frequencies,
            magnitudes,
            dominant_frequencies,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sample_buffer_creation() {
        let buffer = SampleBuffer::new();
        assert_eq!(buffer.buffer_size, SAMPLES_PER_SECOND as usize);
        assert!(!buffer.is_ready());
    }

    #[test]
    fn test_sample_buffer_add_sample() {
        let mut buffer = SampleBuffer::new();
        let sample = Sample {
            x: 100,
            y: 200,
            z: 300,
        };

        buffer.add_sample(&sample);
        assert_eq!(buffer.x_samples.len(), 1);
        assert_eq!(buffer.y_samples.len(), 1);
        assert_eq!(buffer.z_samples.len(), 1);
    }

    #[test]
    fn test_fft_analysis_with_sine_wave() {
        let mut buffer = SampleBuffer::new();

        // Generate a sine wave at 10 Hz
        let freq = 10.0;
        let sample_rate = SAMPLES_PER_SECOND as f64;

        for i in 0..SAMPLES_PER_SECOND as usize {
            let t = i as f64 / sample_rate;
            let value = (2.0 * std::f64::consts::PI * freq * t).sin() * 1000.0;
            let sample = Sample {
                x: value as i16,
                y: 0,
                z: 0,
            };
            buffer.add_sample(&sample);
        }

        let result = buffer.analyze().unwrap();

        // The dominant frequency should be around 10 Hz
        assert!(!result.x.dominant_frequencies.is_empty());
        let (dominant_freq, _) = result.x.dominant_frequencies[0];
        assert!((dominant_freq - freq).abs() < 2.0); // Allow some tolerance
    }
}
