use anyhow::{Context, Result};
use egui_plot::PlotPoint;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::io::{AsyncRead, AsyncReadExt};
use tokio::sync::{mpsc, Mutex};

use crate::csv_logger::CsvLogger;
use crate::gui::{LabrecordApp, SensorData};
use crate::interface::{BURST_SAMPLES, PACKET_SIZE, SAMPLES_PER_SECOND};

use crate::sample::{packet_to_samples, Sample};
use crate::{wire, RUNNING};

// Constants
const TIME_PER_SAMPLE: Duration = Duration::from_micros(1_000_000 / SAMPLES_PER_SECOND as u64);

/// Debug function to print packet data in hex and ASCII
pub fn data_print(packet: &[u8]) {
    for chunk in packet.chunks(48) {
        // print packet in hex and ascii, with hex and ascii perfectly aligned (one line each)
        for byte in chunk.iter() {
            print!("{:02X} ", byte);
        }
        println!();
        for byte in chunk.iter() {
            print!(
                " {} ",
                if byte.is_ascii_graphic() {
                    *byte as char
                } else {
                    '.'
                }
            );
        }
        println!();
    }
}

/// Add samples to sensor data for GUI visualization
pub async fn add_samples(sensor: &Arc<Mutex<SensorData>>, time_offset: f64, sample: &Sample) {
    let mut sensor = sensor.lock().await;
    sensor.x.push(PlotPoint {
        x: time_offset,
        y: sample.x as f64,
    });
    sensor.y.push(PlotPoint {
        x: time_offset,
        y: sample.y as f64,
    });
    sensor.z.push(PlotPoint {
        x: time_offset,
        y: sample.z as f64,
    });
}

/// Synchronize to packet boundary by finding "ADXL" header
pub async fn synchronize_to_packet_boundary(
    stream: &mut Box<dyn AsyncRead + Unpin + Send>,
) -> Result<()> {
    println!("Synchronizing to packet boundary...");
    let mut packet_buffer = vec![0u8; PACKET_SIZE as usize];
    stream.read_exact(&mut packet_buffer).await?;
    let Some(start) = packet_buffer.windows(4).position(|w| w == b"ADXL") else {
        anyhow::bail!("No magic header found");
    };
    // skip enough bytes to be on packet boundary
    let skip = start;
    stream
        .read_exact(&mut packet_buffer[..skip])
        .await
        .context("Failed to read from stream")?;

    println!("Synchronized to packet boundary");
    Ok(())
}

/// Data structure for processed sample bursts sent between UART collection and processing tasks
#[derive(Debug, Clone)]
pub struct ProcessedBurst {
    pub sensor_id: u8,
    pub samples: Vec<Sample>,
    pub time_offsets: Vec<f64>,
    pub unix_times_us: Vec<u128>,
}

/// UART collection task - reads packets from UART, processes them, and sends processed bursts over a channel
pub async fn uart_collection_task(
    mut stream: Box<dyn AsyncRead + Unpin + Send>,
    burst_sender: mpsc::Sender<ProcessedBurst>,
) -> Result<()> {
    println!("Starting UART collection task...");

    let mut packet_buffer = vec![0u8; PACKET_SIZE as usize];
    let batch_start = Instant::now() - TIME_PER_SAMPLE * BURST_SAMPLES as u32;

    // Synchronize to packet boundary by finding "ADXL" header
    for i in 0..10 {
        println!("Synchronizing to packet boundary... (try {})", i);
        if let Err(e) = synchronize_to_packet_boundary(&mut stream).await {
            println!("Failed to synchronize: {}", e);
            if i == 9 {
                anyhow::bail!("Failed to synchronize after 10 tries");
            }
        } else {
            break;
        }
    }

    // Process packets for one hour (approximately)
    let samples_per_hour = SAMPLES_PER_SECOND as usize * 60 * 60;
    let mut num_samples = 0usize;

    loop {
        if !RUNNING.load(std::sync::atomic::Ordering::SeqCst) {
            break;
        }
        if num_samples >= samples_per_hour {
            println!("Collected {} samples (1h), rotating files...", num_samples);
            break;
        }

        // Read one complete packet
        stream
            .read_exact(&mut packet_buffer)
            .await
            .context("Failed to read packet")?;

        // Parse the packet using the wire module
        let packet = match wire::parse_uart_packet(&packet_buffer) {
            Ok(p) => p,
            Err(e) => {
                println!("Failed to parse packet {}", e);
                data_print(&packet_buffer);
                // Try to resynchronize
                break;
            }
        };

        // Convert packet to samples
        let samples = packet_to_samples(&packet);
        num_samples += samples.len();

        // Process samples and create timing information
        let now = Instant::now();
        let now_sys = SystemTime::now();
        let sensor_id = packet.sensor_id;

        let mut time_offsets = Vec::with_capacity(samples.len());
        let mut unix_times_us = Vec::with_capacity(samples.len());

        // Calculate time offset and unix time for each sample
        for i in 0..samples.len() {
            let time = now - (samples.len() - i) as u32 * TIME_PER_SAMPLE;
            let time_offset = time.saturating_duration_since(batch_start);
            let time_sys = now_sys - (samples.len() - i) as u32 * TIME_PER_SAMPLE;
            let unix_time_us = time_sys
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap()
                .as_micros();

            time_offsets.push(time_offset.as_secs_f64());
            unix_times_us.push(unix_time_us);
        }

        // Create processed burst
        let processed_burst = ProcessedBurst {
            sensor_id,
            samples,
            time_offsets,
            unix_times_us,
        };

        // Send processed burst to processing task
        if let Err(_) = burst_sender.send(processed_burst).await {
            println!("Data processing task has stopped, ending UART collection");
            break;
        }

        // Print progress occasionally
        if num_samples % 50_000 == 0 {
            println!("Collected {} samples", num_samples);
        }
    }

    println!("UART collection task finished");
    Ok(())
}

/// Data processing task - consumes processed bursts from channel and handles GUI/CSV logging
pub async fn data_processing_task(
    mut burst_receiver: mpsc::Receiver<ProcessedBurst>,
    mut csv_logger1: CsvLogger,
    mut csv_logger2: CsvLogger,
    gui: LabrecordApp,
) -> Result<()> {
    println!("Starting data processing task...");

    let mut processed_samples = 0usize;

    while let Some(processed_burst) = burst_receiver.recv().await {
        if !RUNNING.load(std::sync::atomic::Ordering::SeqCst) {
            break;
        }

        let ProcessedBurst {
            sensor_id,
            samples,
            time_offsets,
            unix_times_us,
        } = processed_burst;

        processed_samples += samples.len();

        // Process samples based on sensor ID
        match sensor_id {
            1 => {
                // Process and log Sensor 1 samples
                for (i, sample) in samples.iter().enumerate() {
                    let time_offset = time_offsets[i];
                    let unix_time_us = unix_times_us[i];

                    // Update GUI
                    add_samples(&gui.sensor1, time_offset, sample).await;

                    // Log to CSV
                    csv_logger1
                        .log(unix_time_us, sample)
                        .await
                        .context("Failed to log sensor 1 sample")?;
                }
            }
            2 => {
                // Process and log Sensor 2 samples
                for (i, sample) in samples.iter().enumerate() {
                    let time_offset = time_offsets[i];
                    let unix_time_us = unix_times_us[i];

                    // Update GUI
                    add_samples(&gui.sensor2, time_offset, sample).await;

                    // Log to CSV
                    csv_logger2
                        .log(unix_time_us, sample)
                        .await
                        .context("Failed to log sensor 2 sample")?;
                }
            }
            _ => {
                println!("Warning: Invalid sensor ID: {}", sensor_id);
                continue;
            }
        }

        // Print progress occasionally
        if processed_samples % 50_000 == 0 {
            println!("Processed {} samples", processed_samples);
        }
    }

    // Flush CSV loggers before finishing
    let _ = csv_logger1.flush().await;
    let _ = csv_logger2.flush().await;

    println!("Data processing task finished");
    Ok(())
}

/// Process a batch of packets for data collection using separated tasks
pub async fn do_batch(
    stream: Box<dyn AsyncRead + Unpin + Send>,
    csv_logger1: CsvLogger,
    csv_logger2: CsvLogger,
    gui: &LabrecordApp,
) -> Result<()> {
    println!("Starting batch processing with separated tasks...");

    // Create channel for communication between UART collection and data processing tasks
    let (burst_sender, burst_receiver) = mpsc::channel::<ProcessedBurst>(1000);

    // Clone GUI for the data processing task
    let gui_clone = gui.clone();

    // Spawn the UART collection task
    let uart_task = tokio::spawn(async move { uart_collection_task(stream, burst_sender).await });

    // Spawn the data processing task
    let processing_task = tokio::spawn(async move {
        data_processing_task(burst_receiver, csv_logger1, csv_logger2, gui_clone).await
    });

    // Wait for both tasks to complete
    let (uart_result, processing_result) = tokio::join!(uart_task, processing_task);

    // Handle results and check for errors
    let uart_error = match &uart_result {
        Ok(Ok(())) => {
            println!("UART collection task completed successfully");
            None
        }
        Ok(Err(e)) => {
            println!("UART collection task failed: {}", e);
            Some(format!("UART collection failed: {}", e))
        }
        Err(e) => {
            println!("UART collection task panicked: {}", e);
            Some(format!("UART collection panicked: {}", e))
        }
    };

    let processing_error = match &processing_result {
        Ok(Ok(())) => {
            println!("Data processing task completed successfully");
            None
        }
        Ok(Err(e)) => {
            println!("Data processing task failed: {}", e);
            Some(format!("Data processing failed: {}", e))
        }
        Err(e) => {
            println!("Data processing task panicked: {}", e);
            Some(format!("Data processing panicked: {}", e))
        }
    };

    // Return error if either task failed
    if let Some(error_msg) = uart_error.or(processing_error) {
        anyhow::bail!(error_msg);
    }

    println!("Batch processing completed");
    Ok(())
}
